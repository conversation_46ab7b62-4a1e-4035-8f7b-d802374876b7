import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  Card,
  Typography,
  Button,
  Icon,
  Form,
  FormItem,
  Input,
  Select,
  Switch,
  Textarea,
} from '@/shared/components/common';

import { useFormErrors } from '@/shared/hooks/form';
import { cloudStorageProviderConfigurationSchema } from '../schemas';
import { CLOUD_STORAGE_PROVIDER_TYPES } from '../constants';
import { useCreateCloudStorageProvider, useUpdateCloudStorageProvider, useTestCloudStorageProviderWithConfig } from '../hooks';
import type { CloudStorageProviderConfiguration, CloudStorageProviderFormData } from '../types';

interface CloudStorageProviderFormProps {
  provider?: CloudStorageProviderConfiguration;
  onSuccess?: () => void;
  onCancel?: () => void;
}

/**
 * Form tạo/chỉnh sửa Cloud Storage Provider
 */
const CloudStorageProviderForm: React.FC<CloudStorageProviderFormProps> = ({
  provider,
  onSuccess,
  onCancel
}) => {
  const { t } = useTranslation(['integration', 'common']);
  const { formRef, setFormErrors } = useFormErrors<CloudStorageProviderFormData>();

  const defaultValues: CloudStorageProviderFormData = {
    providerType: provider?.providerType || 'google-drive',
    providerName: provider?.providerName || '',
    clientId: provider?.clientId || '',
    clientSecret: provider?.clientSecret || '',
    refreshToken: provider?.refreshToken || '',
    rootFolderId: provider?.rootFolderId || '',
    isActive: provider?.isActive ?? true,
    autoSync: provider?.autoSync ?? false,
    syncFolders: provider?.syncFolders ? JSON.stringify(provider.syncFolders) : '',
  };

  const [isTestingConnection, setIsTestingConnection] = useState(false);

  const createMutation = useCreateCloudStorageProvider();
  const updateMutation = useUpdateCloudStorageProvider();
  const testMutation = useTestCloudStorageProviderWithConfig();

  const isEditing = !!provider;
  const isLoading = createMutation.isPending || updateMutation.isPending;

  // Provider options
  const providerOptions = Object.values(CLOUD_STORAGE_PROVIDER_TYPES).map(provider => ({
    value: provider.id,
    label: provider.displayName,
  }));

  const selectedProvider = CLOUD_STORAGE_PROVIDER_TYPES[defaultValues.providerType];

  const handleTestConnection = async () => {
    try {
      setIsTestingConnection(true);

      // Get current form values
      const currentFormValues = formRef.current?.getValues();
      if (!currentFormValues) {
        console.error('Could not get form values');
        return;
      }

      // Validate form data first
      const validatedData = cloudStorageProviderConfigurationSchema.parse(currentFormValues);

      // Parse sync folders
      if (validatedData.syncFolders) {
        try {
          JSON.parse(validatedData.syncFolders);
        } catch {
          setFormErrors({ syncFolders: t('integration:cloudStorage.validation.syncFolders.invalidJson') });
          return;
        }
      }

      const testData = {
        storageConfig: {
          providerType: validatedData.providerType,
          providerName: validatedData.providerName,
          clientId: validatedData.clientId,
          clientSecret: validatedData.clientSecret,
          refreshToken: validatedData.refreshToken,
          rootFolderId: validatedData.rootFolderId,
        },
        testInfo: {
          testFolderName: 'RedAI Test Folder',
          testFileName: 'test-connection.txt',
        },
      };

      const result = await testMutation.mutateAsync(testData);

      if (result.result?.result?.success) {
        // TODO: Show success toast
        console.log('Test connection successful:', result.result.result);
      } else {
        // TODO: Show error toast
        console.error('Test connection failed:', result.result?.result?.message);
      }
    } catch (error: unknown) {
      console.error('Test connection error:', error);
      if (error && typeof error === 'object' && 'issues' in error) {
        // Zod validation errors
        const fieldErrors: Partial<Record<keyof CloudStorageProviderFormData, string>> = {};
        const zodError = error as { issues: Array<{ path: string[]; message: string }> };
        zodError.issues.forEach((issue) => {
          const field = issue.path[0] as keyof CloudStorageProviderFormData;
          fieldErrors[field] = issue.message;
        });
        setFormErrors(fieldErrors);
      }
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleSubmit = async (data: CloudStorageProviderFormData) => {
    try {
      // Validate form data
      const validatedData = cloudStorageProviderConfigurationSchema.parse(data);

      // Parse sync folders
      let syncFolders: string[] = [];
      if (validatedData.syncFolders) {
        try {
          syncFolders = JSON.parse(validatedData.syncFolders);
        } catch {
          setFormErrors({ syncFolders: t('integration:cloudStorage.validation.syncFolders.invalidJson') });
          return;
        }
      }

      const submitData = {
        providerType: validatedData.providerType,
        providerName: validatedData.providerName,
        clientId: validatedData.clientId,
        clientSecret: validatedData.clientSecret,
        refreshToken: validatedData.refreshToken,
        rootFolderId: validatedData.rootFolderId,
        isActive: validatedData.isActive,
        autoSync: validatedData.autoSync,
        syncFolders,
      };

      if (isEditing && provider) {
        await updateMutation.mutateAsync({ id: provider.id, data: submitData });
      } else {
        await createMutation.mutateAsync(submitData);
      }

      onSuccess?.();
    } catch (error: unknown) {
      console.error('Form submission error:', error);
      if (error && typeof error === 'object' && 'issues' in error) {
        // Zod validation errors
        const fieldErrors: Partial<Record<keyof CloudStorageProviderFormData, string>> = {};
        const zodError = error as { issues: Array<{ path: string[]; message: string }> };
        zodError.issues.forEach((issue) => {
          const field = issue.path[0] as keyof CloudStorageProviderFormData;
          fieldErrors[field] = issue.message;
        });
        setFormErrors(fieldErrors);
      }
    }
  };

  return (
    <Card className="w-full">
      <div className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <Icon name="cloud" size="lg" className="text-primary" />
          <Typography variant="h3">
            {isEditing
              ? t('integration:cloudStorage.form.editTitle')
              : t('integration:cloudStorage.form.createTitle')
            }
          </Typography>
        </div>

        <Form
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          ref={formRef as any}
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          onSubmit={handleSubmit as any}
          schema={cloudStorageProviderConfigurationSchema}
          defaultValues={defaultValues}
          className="space-y-6"
        >
          {/* Provider Type */}
          <FormItem
            label={t('integration:cloudStorage.form.providerType.label')}
            name="providerType"
            required
            helpText={t('integration:cloudStorage.form.providerType.helpText')}
          >
            <Select
              options={providerOptions}
              placeholder={t('integration:cloudStorage.form.providerType.placeholder')}
              fullWidth
              disabled={isEditing} // Cannot change provider type when editing
            />
          </FormItem>

          {/* Provider Name */}
          <FormItem
            label={t('integration:cloudStorage.form.providerName.label')}
            name="providerName"
            required
            helpText={t('integration:cloudStorage.form.providerName.helpText')}
          >
            <Input
              type="text"
              placeholder={t('integration:cloudStorage.form.providerName.placeholder')}
              fullWidth
            />
          </FormItem>

          {/* Client ID */}
          <FormItem
            label={t('integration:cloudStorage.form.clientId.label')}
            name="clientId"
            required
            helpText={t('integration:cloudStorage.form.clientId.helpText')}
          >
            <Input
              type="text"
              placeholder={t('integration:cloudStorage.form.clientId.placeholder')}
              fullWidth
            />
          </FormItem>

          {/* Client Secret */}
          <FormItem
            label={t('integration:cloudStorage.form.clientSecret.label')}
            name="clientSecret"
            required
            helpText={t('integration:cloudStorage.form.clientSecret.helpText')}
          >
            <Input
              type="password"
              placeholder={t('integration:cloudStorage.form.clientSecret.placeholder')}
              fullWidth
            />
          </FormItem>

          {/* Refresh Token */}
          <FormItem
            label={t('integration:cloudStorage.form.refreshToken.label')}
            name="refreshToken"
            required
            helpText={t('integration:cloudStorage.form.refreshToken.helpText')}
          >
            <Textarea
              placeholder={t('integration:cloudStorage.form.refreshToken.placeholder')}
              rows={3}
              fullWidth
            />
          </FormItem>

          {/* Root Folder ID */}
          <FormItem
            label={t('integration:cloudStorage.form.rootFolderId.label')}
            name="rootFolderId"
            helpText={t('integration:cloudStorage.form.rootFolderId.helpText')}
          >
            <Input
              type="text"
              placeholder={t('integration:cloudStorage.form.rootFolderId.placeholder')}
              fullWidth
            />
          </FormItem>

          {/* Sync Folders */}
          <FormItem
            label={t('integration:cloudStorage.form.syncFolders.label')}
            name="syncFolders"
            helpText={t('integration:cloudStorage.form.syncFolders.helpText')}
          >
            <Textarea
              placeholder={t('integration:cloudStorage.form.syncFolders.placeholder')}
              rows={3}
              fullWidth
            />
          </FormItem>

          {/* Settings */}
          <div className="space-y-4">
            <Typography variant="h6">
              {t('common:settings')}
            </Typography>

            {/* Is Active */}
            <FormItem
              label={t('integration:cloudStorage.form.isActive.label')}
              name="isActive"
              helpText={t('integration:cloudStorage.form.isActive.helpText')}
            >
              <Switch />
            </FormItem>

            {/* Auto Sync */}
            <FormItem
              label={t('integration:cloudStorage.form.autoSync.label')}
              name="autoSync"
              helpText={t('integration:cloudStorage.form.autoSync.helpText')}
            >
              <Switch />
            </FormItem>
          </div>

          {/* Provider Info */}
          {selectedProvider && (
            <Card className="p-4 bg-muted/50">
              <div className="flex items-start gap-3">
                <Icon name="info" size="sm" className="text-primary mt-1" />
                <div className="space-y-2">
                  <Typography variant="subtitle2">
                    {selectedProvider.displayName}
                  </Typography>
                  <Typography variant="caption" className="text-muted-foreground">
                    {selectedProvider.description}
                  </Typography>
                  <div className="flex flex-wrap gap-2 mt-2">
                    <Typography variant="caption" className="text-muted-foreground">
                      <strong>{t('integration:cloudStorage.details.storageQuota')}:</strong> {selectedProvider.maxFileSize}
                    </Typography>
                  </div>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {selectedProvider.supportedFeatures.map(feature => (
                      <span
                        key={feature}
                        className="px-2 py-1 bg-primary/10 text-primary text-xs rounded"
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </Card>
          )}

          {/* Actions */}
          <div className="flex gap-3 pt-6 border-t">
            {/* Test Connection */}
            <Button
              type="button"
              variant="outline"
              onClick={handleTestConnection}
              disabled={isTestingConnection || isLoading}
              className="flex-1"
            >
              {isTestingConnection ? (
                <>
                  <Icon name="loading" size="sm" className="mr-2" />
                  {t('common:testing')}
                </>
              ) : (
                <>
                  <Icon name="zap" size="sm" className="mr-2" />
                  {t('integration:cloudStorage.form.testConnection')}
                </>
              )}
            </Button>

            {/* Cancel */}
            {onCancel && (
              <Button
                type="button"
                variant="ghost"
                onClick={onCancel}
                disabled={isLoading}
                className="flex-1"
              >
                {t('common:cancel')}
              </Button>
            )}

            {/* Submit */}
            <Button
              type="submit"
              variant="primary"
              disabled={isLoading}
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <Icon name="loading" size="sm" className="mr-2" />
                  {isEditing ? t('common:updating') : t('common:creating')}
                </>
              ) : (
                <>
                  <Icon name="save" size="sm" className="mr-2" />
                  {isEditing ? t('common:update') : t('common:create')}
                </>
              )}
            </Button>
          </div>
        </Form>
      </div>
    </Card>
  );
};

export default CloudStorageProviderForm;
