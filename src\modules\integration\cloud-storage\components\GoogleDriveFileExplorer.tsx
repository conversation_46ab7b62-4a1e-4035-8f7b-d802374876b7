import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  Table,
  Input,
  EmptyState,
} from '@/shared/components/common';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useFiles, useFolders, useDeleteFile, useDeleteFolder, useCreateFolder } from '../hooks';
import type { CloudStorageFile, CloudStorageFolder } from '../types';
import { formatDate } from '@/shared/utils/date';
import { formatFileSize } from '@/shared/utils/file-utils';

type FileExplorerItem = (CloudStorageFile | CloudStorageFolder) & { type: 'file' | 'folder' };

interface GoogleDriveFileExplorerProps {
  /**
   * Provider ID
   */
  providerId: number;
  
  /**
   * Current folder ID
   */
  currentFolderId?: string;
  
  /**
   * Callback khi chọn file
   */
  onFileSelect?: (file: CloudStorageFile) => void;
  
  /**
   * Callback khi chọn folder
   */
  onFolderSelect?: (folder: CloudStorageFolder) => void;
  
  /**
   * Callback khi thay đổi folder
   */
  onFolderChange?: (folderId: string | undefined) => void;
  
  /**
   * Cho phép upload
   */
  allowUpload?: boolean;
  
  /**
   * Cho phép tạo folder
   */
  allowCreateFolder?: boolean;
  
  /**
   * Cho phép xóa
   */
  allowDelete?: boolean;
  
  /**
   * Mode hiển thị
   */
  mode?: 'explorer' | 'selector';
}

/**
 * Component duyệt file Google Drive
 */
const GoogleDriveFileExplorer: React.FC<GoogleDriveFileExplorerProps> = ({
  providerId,
  currentFolderId,
  onFileSelect,
  onFolderSelect,
  onFolderChange,
  allowUpload = true,
  allowCreateFolder = true,
  allowDelete = true,
  mode = 'explorer',
}) => {
  const { t } = useTranslation(['integration', 'common']);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateFolderForm, setShowCreateFolderForm] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');

  // Hooks
  const { data: filesData, isLoading: filesLoading } = useFiles(providerId, currentFolderId);
  const { data: foldersData, isLoading: foldersLoading } = useFolders(providerId, currentFolderId);
  const deleteFileMutation = useDeleteFile();
  const deleteFolderMutation = useDeleteFolder();
  const createFolderMutation = useCreateFolder();

  const isLoading = filesLoading || foldersLoading;

  // Combine files and folders for table
  const items = useMemo(() => {
    const files = filesData?.result?.result || [];
    const folders = foldersData?.result?.result || [];
    const allItems = [
      ...folders.map((folder: any) => ({ ...folder, type: 'folder' as const })),
      ...files.map((file: any) => ({ ...file, type: 'file' as const })),
    ];

    // Filter by search term
    if (searchTerm) {
      return allItems.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return allItems;
  }, [filesData?.result?.result, foldersData?.result?.result, searchTerm]);

  const handleFolderOpen = useCallback((folder: any) => {
    onFolderChange?.(folder.id);
  }, [onFolderChange]);

  const handleFileDownload = useCallback((file: any) => {
    // TODO: Implement file download
    console.log('Download file:', file);
  }, []);

  const handleDeleteFile = useCallback(async (file: any) => {
    if (window.confirm(t('integration:cloudStorage.modal.deleteDescription', { name: file.name }))) {
      try {
        await deleteFileMutation.mutateAsync({ providerId, fileId: file.id });
      } catch (error) {
        console.error('Delete file error:', error);
      }
    }
  }, [t, deleteFileMutation, providerId]);

  const handleDeleteFolder = useCallback(async (folder: any) => {
    if (window.confirm(t('integration:cloudStorage.modal.deleteDescription', { name: folder.name }))) {
      try {
        await deleteFolderMutation.mutateAsync({ providerId, folderId: folder.id });
      } catch (error) {
        console.error('Delete folder error:', error);
      }
    }
  }, [t, deleteFolderMutation, providerId]);

  // Table columns
  const columns = useMemo(() => [
    {
      title: t('integration:cloudStorage.details.fileName'),
      dataIndex: 'name',
      key: 'name',
      render: (_: unknown, record: FileExplorerItem) => (
        <div className="flex items-center gap-2">
          <Icon
            name={record.type === 'folder' ? 'folder' : 'file'}
            size="sm"
            className={record.type === 'folder' ? 'text-blue-600' : 'text-gray-600'}
          />
          <span className="truncate">{record.name}</span>
        </div>
      ),
    },
    {
      title: t('integration:cloudStorage.details.fileSize'),
      dataIndex: 'size',
      key: 'size',
      render: (_: unknown, record: FileExplorerItem) =>
        record.type === 'file' && (record as any).size ? formatFileSize((record as any).size) : '-',
    },
    {
      title: t('integration:cloudStorage.details.lastModified'),
      dataIndex: 'modifiedTime',
      key: 'modifiedTime',
      render: (_: unknown, record: FileExplorerItem) =>
        (record as any).modifiedAt ? formatDate((record as any).modifiedAt) : '-',
    },
    {
      title: t('common:actions'),
      key: 'actions',
      render: (_: unknown, record: FileExplorerItem) => (
        <div className="flex items-center gap-2">
          {record.type === 'folder' ? (
            <>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleFolderOpen(record)}
              >
                <Icon name="folder-open" size="sm" />
              </Button>
              {mode === 'selector' && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onFolderSelect?.(record as any)}
                >
                  <Icon name="check" size="sm" />
                </Button>
              )}
              {allowDelete && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDeleteFolder(record)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Icon name="trash" size="sm" />
                </Button>
              )}
            </>
          ) : (
            <>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleFileDownload(record)}
              >
                <Icon name="download" size="sm" />
              </Button>
              {mode === 'selector' && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onFileSelect?.(record)}
                >
                  <Icon name="check" size="sm" />
                </Button>
              )}
              {allowDelete && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDeleteFile(record)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Icon name="trash" size="sm" />
                </Button>
              )}
            </>
          )}
        </div>
      ),
    },
  ], [t, mode, allowDelete, onFileSelect, onFolderSelect, handleDeleteFile, handleDeleteFolder, handleFolderOpen, handleFileDownload]);

  // Data table configuration
  const dataTable = useDataTable(useDataTableConfig({
    columns,
    createQueryParams: (params) => params
  }));

  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) return;

    try {
      await createFolderMutation.mutateAsync({
        providerId,
        data: {
          folderName: newFolderName,
          parentFolderId: currentFolderId,
        },
      });
      setNewFolderName('');
      setShowCreateFolderForm(false);
    } catch (error) {
      console.error('Create folder error:', error);
    }
  };

  const handleBackToParent = () => {
    // TODO: Implement navigation to parent folder
    onFolderChange?.(undefined);
  };

  return (
    <div className="w-full space-y-4">
      {/* Header */}
      <Card>
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Icon name="cloud" size="lg" className="text-primary" />
              <Typography variant="h5">
                Google Drive Explorer
              </Typography>
            </div>

            <div className="flex items-center gap-2">
              {currentFolderId && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBackToParent}
                >
                  <Icon name="arrow-left" size="sm" className="mr-2" />
                  {t('common:back')}
                </Button>
              )}

              {allowCreateFolder && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowCreateFolderForm(true)}
                >
                  <Icon name="folder-plus" size="sm" className="mr-2" />
                  {t('integration:cloudStorage.actions.createFolder')}
                </Button>
              )}

              {allowUpload && (
                <Button
                  variant="primary"
                  size="sm"
                >
                  <Icon name="upload" size="sm" className="mr-2" />
                  {t('integration:cloudStorage.actions.upload')}
                </Button>
              )}
            </div>
          </div>

          {/* Search */}
          <Input
            type="text"
            placeholder={t('integration:cloudStorage.list.searchPlaceholder')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            fullWidth
          />

          {/* Create Folder Form */}
          {showCreateFolderForm && (
            <div className="mt-4 p-4 border rounded-lg bg-muted/50">
              <div className="flex items-center gap-2">
                <Input
                  type="text"
                  placeholder={t('integration:cloudStorage.validation.folderName.required')}
                  value={newFolderName}
                  onChange={(e) => setNewFolderName(e.target.value)}
                  className="flex-1"
                />
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleCreateFolder}
                  disabled={!newFolderName.trim() || createFolderMutation.isPending}
                >
                  {createFolderMutation.isPending ? (
                    <Icon name="loading" size="sm" />
                  ) : (
                    <Icon name="check" size="sm" />
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setShowCreateFolderForm(false);
                    setNewFolderName('');
                  }}
                >
                  <Icon name="x" size="sm" />
                </Button>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Content */}
      <Card>
        {items.length > 0 ? (
          <div className="p-4">
            <Table
              columns={dataTable.columnVisibility.visibleTableColumns}
              data={items}
              loading={isLoading}
            />
          </div>
        ) : (
          <div className="p-8">
            <EmptyState
              icon="folder"
              title={t('integration:cloudStorage.empty.noFiles')}
              description={t('integration:cloudStorage.empty.noFilesDescription')}
            />
          </div>
        )}
      </Card>
    </div>
  );
};

export default GoogleDriveFileExplorer;
